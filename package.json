{"name": "dailylog-lite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "pinia": "^2.2.4", "vue": "^3.5.17", "vue-router": "^4.4.5"}, "devDependencies": {"@eslint/js": "^9.17.0", "@iconify-json/tabler": "^1.2.20", "@types/file-saver": "^2.0.7", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "@vueuse/core": "^13.6.0", "autoprefixer": "^10.4.21", "daisyui": "^5.0.50", "date-fns": "^4.1.0", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-vue": "^9.32.0", "file-saver": "^2.0.5", "localforage": "^1.10.0", "lodash-es": "^4.17.21", "naive-ui": "^2.42.0", "postcss": "^8.5.6", "prettier": "^3.4.2", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "unplugin-icons": "^22.2.0", "uuid": "^11.1.0", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}