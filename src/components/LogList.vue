<template>
  <div class="log-list">
    <div v-if="loading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <div v-else-if="logs.length === 0" class="text-center py-8">
      <div class="text-gray-500">暂无日志记录</div>
    </div>

    <div v-else class="space-y-4">
      <div v-for="log in logs" :key="log.id" class="card bg-base-100 shadow-sm">
        <div class="card-body">
          <div class="flex justify-between items-start">
            <div>
              <h3 class="card-title text-lg">{{ log.title }}</h3>
              <p class="text-sm text-gray-500">{{ formatDate(log.date) }}</p>
            </div>
            <div class="badge" :class="getMoodBadgeClass(log.mood)">
              {{ getMoodLabel(log.mood) }}
            </div>
          </div>
          
          <p class="text-gray-700">{{ log.content }}</p>
          
          <div class="flex items-center gap-2">
            <div class="badge badge-outline">{{ getCategoryName(log.categoryId) }}</div>
            <div v-for="tag in log.tags" :key="tag" class="badge badge-ghost">
              {{ tag }}
            </div>
          </div>

          <div class="card-actions justify-end">
            <button class="btn btn-sm btn-ghost" @click="$emit('edit', log)">
              编辑
            </button>
            <button class="btn btn-sm btn-ghost text-error" @click="$emit('delete', log)">
              删除
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCategoryStore } from '../stores/category'
import type { Log } from '../types'
import { formatDate } from '../utils/date'

interface Props {
  logs: Log[]
  loading?: boolean
}

withDefaults(defineProps<Props>(), {
  loading: false
})

defineEmits<{
  edit: [log: Log]
  delete: [log: Log]
}>()

const categoryStore = useCategoryStore()

const getCategoryName = (categoryId: string) => {
  const category = categoryStore.getCategory(categoryId)
  return category?.name || '未分类'
}

const getMoodBadgeClass = (mood: string) => {
  const classes = {
    happy: 'badge-success',
    sad: 'badge-error',
    angry: 'badge-warning',
    neutral: 'badge-ghost',
    excited: 'badge-info',
    tired: 'badge-secondary',
    anxious: 'badge-warning'
  }
  return classes[mood as keyof typeof classes] || 'badge-ghost'
}

const getMoodLabel = (mood: string) => {
  const labels = {
    happy: '开心',
    sad: '难过',
    angry: '生气',
    neutral: '平静',
    excited: '兴奋',
    tired: '疲惫',
    anxious: '焦虑'
  }
  return labels[mood as keyof typeof labels] || mood
}
</script>

<style scoped>
.log-list {
  max-width: 100%;
}
</style>
