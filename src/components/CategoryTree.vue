<template>
  <div class="category-tree">
    <div class="tree-header">
      <h3>分类管理</h3>
      <button @click="showAddModal = true" class="btn btn-sm btn-primary">
        添加分类
      </button>
    </div>

    <div class="tree-content">
      <ul class="menu menu-compact">
        <li v-for="category in rootCategories" :key="category.id">
          <details>
            <summary>
              <span class="flex items-center gap-2">
                <span :style="{ color: category.color }">●</span>
                {{ category.name }}
              </span>
            </summary>
            <ul>
              <li v-for="child in getChildren(category.id)" :key="child.id">
                <a href="#" @click.prevent="selectCategory(child.id)">
                  <span :style="{ color: child.color }">●</span>
                  {{ child.name }}
                </a>
              </li>
            </ul>
          </details>
        </li>
      </ul>
    </div>

    <!-- 添加/编辑分类模态框 -->
    <div v-if="showAddModal" class="modal modal-open">
      <div class="modal-box">
        <h3 class="font-bold text-lg">{{ editingCategory ? '编辑分类' : '添加分类' }}</h3>
        <form @submit.prevent="handleSubmit">
          <div class="form-control">
            <label class="label">
              <span class="label-text">名称</span>
            </label>
            <input v-model="formData.name" type="text" class="input input-bordered" required />
          </div>
          <div class="form-control">
            <label class="label">
              <span class="label-text">颜色</span>
            </label>
            <input v-model="formData.color" type="color" class="input input-bordered" />
          </div>
          <div class="form-control">
            <label class="label">
              <span class="label-text">排序</span>
            </label>
            <input v-model.number="formData.sortOrder" type="number" class="input input-bordered" min="0" />
          </div>
          <div class="modal-action">
            <button type="button" class="btn" @click="showAddModal = false">取消</button>
            <button type="submit" class="btn btn-primary">确定</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useCategoryStore } from '../stores/category'
import type { Category } from '../types'

const categoryStore = useCategoryStore()

// 状态
const showAddModal = ref(false)
const editingCategory = ref<Category | null>(null)
const formData = ref({
  name: '',
  color: '#22c55e',
  parentId: null as string | null,
  sortOrder: 0
})

// 计算属性
const rootCategories = computed(() => categoryStore.rootCategories)

// 获取子分类
const getChildren = (parentId: string) => {
  return categoryStore.getChildren(parentId)
}

// 选择分类
const selectCategory = (id: string) => {
  categoryStore.selectCategory(id)
}

// 处理提交
const handleSubmit = async () => {
  try {
    if (editingCategory.value) {
      await categoryStore.updateCategory(editingCategory.value.id, formData.value)
    } else {
      await categoryStore.createCategory({
        ...formData.value,
        icon: 'folder',
        isActive: true
      })
    }
    
    showAddModal.value = false
    resetForm()
  } catch (error) {
    console.error('保存分类失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  editingCategory.value = null
  formData.value = {
    name: '',
    color: '#22c55e',
    parentId: null,
    sortOrder: 0
  }
}

// 初始化
onMounted(async () => {
  await categoryStore.loadCategories()
})
</script>

<style scoped>
.category-tree {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.tree-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}
</style>
