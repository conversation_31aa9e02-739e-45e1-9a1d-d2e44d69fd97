<template>
  <div class="summary-panel">
    <div class="stats shadow">
      <div class="stat">
        <div class="stat-figure text-primary">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-8 h-8 stroke-current">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </div>
        <div class="stat-title">总日志数</div>
        <div class="stat-value text-primary">{{ summary.totalLogs }}</div>
      </div>

      <div class="stat">
        <div class="stat-figure text-success">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-8 h-8 stroke-current">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div class="stat-title">已完成</div>
        <div class="stat-value text-success">{{ summary.completedLogs }}</div>
      </div>

      <div class="stat">
        <div class="stat-figure text-warning">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-8 h-8 stroke-current">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div class="stat-title">待完成</div>
        <div class="stat-value text-warning">{{ summary.pendingLogs }}</div>
      </div>
    </div>

    <div class="mt-6">
      <h3 class="text-lg font-semibold mb-4">分类统计</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div v-for="stat in summary.categoryStats" :key="stat.categoryId" class="card bg-base-100 shadow-sm">
          <div class="card-body">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium">{{ stat.categoryName }}</h4>
                <p class="text-sm text-gray-500">{{ stat.logCount }} 条日志</p>
              </div>
              <div class="radial-progress" :style="{ '--value': getCategoryProgress(stat), '--size': '3rem' }">
                {{ Math.round(getCategoryProgress(stat)) }}%
              </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-primary h-2 rounded-full" :style="{ width: getCategoryProgress(stat) + '%' }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-6">
      <h3 class="text-lg font-semibold mb-4">心情分布</h3>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div v-for="stat in summary.moodStats" :key="stat.mood" class="card bg-base-100 shadow-sm">
          <div class="card-body text-center">
            <div class="text-2xl">{{ getMoodEmoji(stat.mood) }}</div>
            <div class="text-sm">{{ getMoodLabel(stat.mood) }}</div>
            <div class="text-lg font-semibold">{{ stat.count }}</div>
            <div class="text-xs text-gray-500">{{ stat.percentage }}%</div>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-6">
      <h3 class="text-lg font-semibold mb-4">最近7天活动</h3>
      <div class="card bg-base-100 shadow-sm">
        <div class="card-body">
          <div class="flex items-end justify-between h-32">
            <div v-for="(day, index) in recentDays" :key="index" class="flex flex-col items-center">
              <div class="flex gap-1 mb-2">
                <div class="w-3 bg-success rounded-t" :style="{ height: getBarHeight(day.completed, day.total) + '%' }"></div>
                <div class="w-3 bg-warning rounded-t" :style="{ height: getBarHeight(day.pending, day.total) + '%' }"></div>
              </div>
              <div class="text-xs text-gray-500">{{ formatDayLabel(day.date) }}</div>
            </div>
          </div>
          <div class="flex justify-center gap-4 text-xs">
            <div class="flex items-center gap-1">
              <div class="w-3 h-3 bg-success rounded"></div>
              <span>已完成</span>
            </div>
            <div class="flex items-center gap-1">
              <div class="w-3 h-3 bg-warning rounded"></div>
              <span>待完成</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useLogStore } from '../stores/log'
import { useCategoryStore } from '../stores/category'
import type { Summary, CategoryStats, MoodStats } from '../types'
import { formatDate } from '../utils/date'

const logStore = useLogStore()
const categoryStore = useCategoryStore()

const summary = computed<Summary>(() => {
  const logs = logStore.logs
  const categories = categoryStore.categories

  const totalLogs = logs.length
  const completedLogs = logs.filter(log => log.isCompleted).length
  const pendingLogs = totalLogs - completedLogs

  // 分类统计
  const categoryStats: CategoryStats[] = categories.map(category => {
    const categoryLogs = logs.filter(log => log.categoryId === category.id)
    const completedCount = categoryLogs.filter(log => log.isCompleted).length
    
    return {
      categoryId: category.id,
      categoryName: category.name,
      logCount: categoryLogs.length,
      completedCount,
      color: category.color
    }
  }).filter(stat => stat.logCount > 0)

  // 心情统计
  const moodCounts = logs.reduce((acc, log) => {
    acc[log.mood] = (acc[log.mood] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const moodStats: MoodStats[] = Object.entries(moodCounts).map(([mood, count]) => ({
    mood: mood as any,
    count,
    percentage: Math.round((count / totalLogs) * 100)
  }))

  return {
    totalLogs,
    completedLogs,
    pendingLogs,
    categoryStats,
    moodStats,
    dateRange: {
      start: new Date(Math.min(...logs.map(log => log.date.getTime()))),
      end: new Date(Math.max(...logs.map(log => log.date.getTime())))
    }
  }
})

const recentDays = computed(() => {
  const days = []
  const today = new Date()
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    date.setHours(0, 0, 0, 0)
    
    const dayLogs = logStore.logs.filter(log => {
      const logDate = new Date(log.date)
      return logDate.toDateString() === date.toDateString()
    })
    
    days.push({
      date,
      total: dayLogs.length,
      completed: dayLogs.filter(log => log.isCompleted).length,
      pending: dayLogs.filter(log => !log.isCompleted).length
    })
  }
  
  return days
})

const getCategoryProgress = (stat: CategoryStats) => {
  return stat.logCount > 0 ? (stat.completedCount / stat.logCount) * 100 : 0
}

const getMoodEmoji = (mood: string) => {
  const emojis = {
    happy: '😊',
    sad: '😢',
    angry: '😠',
    neutral: '😐',
    excited: '🤩',
    tired: '😴',
    anxious: '😰'
  }
  return emojis[mood as keyof typeof emojis] || '😐'
}

const getMoodLabel = (mood: string) => {
  const labels = {
    happy: '开心',
    sad: '难过',
    angry: '生气',
    neutral: '平静',
    excited: '兴奋',
    tired: '疲惫',
    anxious: '焦虑'
  }
  return labels[mood as keyof typeof labels] || mood
}

const getBarHeight = (value: number, total: number) => {
  return total > 0 ? (value / total) * 100 : 0
}

const formatDayLabel = (date: Date) => {
  return formatDate(date, 'MM-DD')
}
</script>

<style scoped>
.summary-panel {
  max-width: 100%;
}
</style>
