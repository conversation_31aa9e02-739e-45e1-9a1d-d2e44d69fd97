<template>
  <div class="log-editor">
    <div class="card bg-base-100 shadow-sm">
      <div class="card-body">
        <h3 class="card-title">{{ props.log ? '编辑日志' : '新建日志' }}</h3>
        
        <form @submit.prevent="handleSubmit">
          <div class="form-control">
            <label class="label">
              <span class="label-text">标题</span>
            </label>
            <input v-model="formData.title" type="text" class="input input-bordered" required />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">日期</span>
            </label>
            <input v-model="formData.date" type="date" class="input input-bordered" required />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">分类</span>
            </label>
            <select v-model="formData.categoryId" class="select select-bordered" required>
              <option value="">选择分类</option>
              <option v-for="category in activeCategories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">心情</span>
            </label>
            <select v-model="formData.mood" class="select select-bordered">
              <option value="happy">开心</option>
              <option value="sad">难过</option>
              <option value="angry">生气</option>
              <option value="neutral">平静</option>
              <option value="excited">兴奋</option>
              <option value="tired">疲惫</option>
              <option value="anxious">焦虑</option>
            </select>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">优先级</span>
            </label>
            <select v-model.number="formData.priority" class="select select-bordered">
              <option :value="1">低</option>
              <option :value="2">中</option>
              <option :value="3">高</option>
              <option :value="4">紧急</option>
            </select>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">标签</span>
            </label>
            <input v-model="tagsInput" type="text" class="input input-bordered" placeholder="用逗号分隔标签" />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">内容</span>
            </label>
            <textarea v-model="formData.content" class="textarea textarea-bordered h-32" required></textarea>
          </div>

          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="label-text">已完成</span>
              <input v-model="formData.isCompleted" type="checkbox" class="checkbox" />
            </label>
          </div>

          <div class="card-actions justify-end mt-4">
            <button type="button" class="btn btn-ghost" @click="$emit('cancel')">取消</button>
            <button type="submit" class="btn btn-primary">保存</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useCategoryStore } from '../stores/category'
import { useLogStore } from '../stores/log'
import type { Log } from '../types'
import type { Mood, Priority } from '../types'

interface Props {
  log?: Log
}

const props = withDefaults(defineProps<Props>(), {
  log: undefined
})

const emit = defineEmits<{
  save: []
  cancel: []
}>()

const categoryStore = useCategoryStore()
const logStore = useLogStore()

const activeCategories = computed(() => categoryStore.activeCategories)

const formData = ref({
  title: '',
  content: '',
  categoryId: '',
  date: new Date().toISOString().split('T')[0],
  tags: [] as string[],
  mood: 'neutral' as Mood,
  priority: 2 as Priority,
  isCompleted: false
})

const tagsInput = ref('')

// 监听编辑日志变化
watch(() => props.log, (newLog) => {
  if (newLog) {
    formData.value = {
      title: newLog.title,
      content: newLog.content,
      categoryId: newLog.categoryId,
      date: new Date(newLog.date).toISOString().split('T')[0],
      tags: [...newLog.tags],
      mood: newLog.mood,
      priority: newLog.priority,
      isCompleted: newLog.isCompleted
    }
    tagsInput.value = newLog.tags.join(', ')
  } else {
    resetForm()
  }
}, { immediate: true })

// 监听标签输入
watch(tagsInput, (newVal) => {
  formData.value.tags = newVal.split(',').map(tag => tag.trim()).filter(tag => tag)
})

// 重置表单
const resetForm = () => {
  formData.value = {
    title: '',
    content: '',
    categoryId: '',
    date: new Date().toISOString().split('T')[0],
    tags: [],
    mood: 'neutral',
    priority: 2,
    isCompleted: false
  }
  tagsInput.value = ''
}

// 处理提交
const handleSubmit = async () => {
  try {
    const logData = {
      ...formData.value,
      date: new Date(formData.value.date)
    }

    if (props.log) {
      await logStore.updateLog(props.log.id, logData)
    } else {
      await logStore.createLog(logData)
    }
    
    emit('save')
  } catch (error) {
    console.error('保存日志失败:', error)
  }
}

// 初始化
onMounted(async () => {
  await categoryStore.loadCategories()
})
</script>

<style scoped>
.log-editor {
  max-width: 100%;
}
</style>
