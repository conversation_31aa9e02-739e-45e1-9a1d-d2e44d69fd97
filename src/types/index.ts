// 数据模型类型定义

// 分类类型
export interface Category {
  id: string
  name: string
  parentId: string | null
  color: string
  icon: string
  sortOrder: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// 日志类型
export interface Log {
  id: string
  title: string
  content: string
  categoryId: string
  date: Date
  tags: string[]
  mood: Mood
  priority: Priority
  isCompleted: boolean
  createdAt: Date
  updatedAt: Date
}

// 心情类型
export type Mood = 'happy' | 'sad' | 'angry' | 'neutral' | 'excited' | 'tired' | 'anxious'

// 优先级类型
export type Priority = 1 | 2 | 3 | 4

// 汇总数据类型
export interface Summary {
  totalLogs: number
  completedLogs: number
  pendingLogs: number
  categoryStats: CategoryStats[]
  moodStats: MoodStats[]
  dateRange: {
    start: Date
    end: Date
  }
}

// 分类统计
export interface CategoryStats {
  categoryId: string
  categoryName: string
  logCount: number
  completedCount: number
  color: string
}

// 心情统计
export interface MoodStats {
  mood: Mood
  count: number
  percentage: number
}

// 导出配置
export interface ExportConfig {
  format: 'json' | 'csv' | 'txt'
  dateRange?: {
    start: Date
    end: Date
  }
  categories?: string[]
  includeCompleted?: boolean
}

// 存储配置
export interface StorageConfig {
  dbName: string
  version: number
  stores: {
    categories: string
    logs: string
    settings: string
  }
}

// 应用设置
export interface AppSettings {
  theme: 'light' | 'dark' | 'auto'
  defaultCategory: string | null
  autoSave: boolean
  exportPath: string
  dateFormat: string
  timeFormat: string
}
