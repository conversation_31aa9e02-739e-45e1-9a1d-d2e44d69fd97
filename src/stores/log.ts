import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Log } from '../types'
import { logStorage } from '../utils/storage'
import { v4 as uuidv4 } from 'uuid'

export const useLogStore = defineStore('log', () => {
  // 状态
  const logs = ref<Log[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const selectedLogId = ref<string | null>(null)
  const filterCategoryId = ref<string | null>(null)
  const searchQuery = ref('')
  const dateRange = ref<{ start: Date; end: Date } | null>(null)

  // 计算属性
  const filteredLogs = computed(() => {
    let result = logs.value

    // 按分类过滤
    if (filterCategoryId.value) {
      result = result.filter(log => log.categoryId === filterCategoryId.value)
    }

    // 按日期范围过滤
    if (dateRange.value) {
      result = result.filter(log => {
        const logDate = new Date(log.date)
        return logDate >= dateRange.value!.start && logDate <= dateRange.value!.end
      })
    }

    // 按搜索查询过滤
    if (searchQuery.value.trim()) {
      const query = searchQuery.value.toLowerCase()
      result = result.filter(log => 
        log.title.toLowerCase().includes(query) ||
        log.content.toLowerCase().includes(query) ||
        log.tags.some(tag => tag.toLowerCase().includes(query))
      )
    }

    return result.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  })

  const completedLogs = computed(() => 
    filteredLogs.value.filter(log => log.isCompleted)
  )

  const pendingLogs = computed(() => 
    filteredLogs.value.filter(log => !log.isCompleted)
  )

  const logMap = computed(() => 
    new Map(logs.value.map(log => [log.id, log]))
  )

  const todayLogs = computed(() => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)
    
    return logs.value.filter(log => {
      const logDate = new Date(log.date)
      return logDate >= today && logDate < tomorrow
    })
  })

  // 加载日志
  const loadLogs = async () => {
    loading.value = true
    error.value = null
    
    try {
      logs.value = await logStorage.getAll()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载日志失败'
    } finally {
      loading.value = false
    }
  }

  // 创建日志
  const createLog = async (logData: Omit<Log, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null
    
    try {
      const newLog: Log = {
        ...logData,
        id: uuidv4(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      await logStorage.save(newLog)
      logs.value.unshift(newLog)
      
      return newLog
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建日志失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新日志
  const updateLog = async (id: string, updates: Partial<Log>) => {
    loading.value = true
    error.value = null
    
    try {
      const log = logMap.value.get(id)
      if (!log) {
        throw new Error('日志不存在')
      }
      
      const updatedLog = {
        ...log,
        ...updates,
        updatedAt: new Date()
      }
      
      await logStorage.save(updatedLog)
      
      const index = logs.value.findIndex(log => log.id === id)
      if (index !== -1) {
        logs.value[index] = updatedLog
      }
      
      return updatedLog
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新日志失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 删除日志
  const deleteLog = async (id: string) => {
    loading.value = true
    error.value = null
    
    try {
      await logStorage.delete(id)
      logs.value = logs.value.filter(log => log.id !== id)
      
      // 如果删除的是当前选中的日志，清除选择
      if (selectedLogId.value === id) {
        selectedLogId.value = null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除日志失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 获取日志
  const getLog = (id: string) => {
    return logMap.value.get(id)
  }

  // 获取分类下的日志
  const getLogsByCategory = (categoryId: string) => {
    return logs.value.filter(log => log.categoryId === categoryId)
  }

  // 获取日期范围内的日志
  const getLogsByDateRange = (start: Date, end: Date) => {
    return logs.value.filter(log => {
      const logDate = new Date(log.date)
      return logDate >= start && logDate <= end
    })
  }

  // 设置过滤器
  const setFilter = (filters: {
    categoryId?: string | null
    searchQuery?: string
    dateRange?: { start: Date; end: Date } | null
  }) => {
    if (filters.categoryId !== undefined) {
      filterCategoryId.value = filters.categoryId
    }
    if (filters.searchQuery !== undefined) {
      searchQuery.value = filters.searchQuery
    }
    if (filters.dateRange !== undefined) {
      dateRange.value = filters.dateRange
    }
  }

  // 清除过滤器
  const clearFilters = () => {
    filterCategoryId.value = null
    searchQuery.value = ''
    dateRange.value = null
  }

  return {
    // 状态
    logs,
    loading,
    error,
    selectedLogId,
    filterCategoryId,
    searchQuery,
    dateRange,
    
    // 计算属性
    filteredLogs,
    completedLogs,
    pendingLogs,
    logMap,
    todayLogs,
    
    // 方法
    loadLogs,
    createLog,
    updateLog,
    deleteLog,
    getLog,
    getLogsByCategory,
    getLogsByDateRange,
    setFilter,
    clearFilters
  }
})
