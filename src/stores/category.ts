import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Category } from '../types'
import { categoryStorage } from '../utils/storage'
import { v4 as uuidv4 } from 'uuid'

export const useCategoryStore = defineStore('category', () => {
  // 状态
  const categories = ref<Category[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const selectedCategoryId = ref<string | null>(null)

  // 计算属性
  const rootCategories = computed(() => 
    categories.value.filter(cat => !cat.parentId)
  )

  const activeCategories = computed(() => 
    categories.value.filter(cat => cat.isActive)
  )

  const categoryMap = computed(() => 
    new Map(categories.value.map(cat => [cat.id, cat]))
  )

  // 获取分类的子分类
  const getChildren = (parentId: string | null) => {
    return categories.value.filter(cat => cat.parentId === parentId)
  }

  // 获取分类的完整路径
  const getCategoryPath = (categoryId: string): Category[] => {
    const path: Category[] = []
    let current = categoryMap.value.get(categoryId)
    
    while (current) {
      path.unshift(current)
      if (current.parentId) {
        current = categoryMap.value.get(current.parentId) || undefined
      } else {
        break
      }
    }
    
    return path
  }

  // 加载分类
  const loadCategories = async () => {
    loading.value = true
    error.value = null
    
    try {
      categories.value = await categoryStorage.getAll()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载分类失败'
    } finally {
      loading.value = false
    }
  }

  // 创建分类
  const createCategory = async (categoryData: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null
    
    try {
      const newCategory: Category = {
        ...categoryData,
        id: uuidv4(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      await categoryStorage.save(newCategory)
      categories.value.push(newCategory)
      
      return newCategory
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建分类失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新分类
  const updateCategory = async (id: string, updates: Partial<Category>) => {
    loading.value = true
    error.value = null
    
    try {
      const category = categoryMap.value.get(id)
      if (!category) {
        throw new Error('分类不存在')
      }
      
      const updatedCategory = {
        ...category,
        ...updates,
        updatedAt: new Date()
      }
      
      await categoryStorage.save(updatedCategory)
      
      const index = categories.value.findIndex(cat => cat.id === id)
      if (index !== -1) {
        categories.value[index] = updatedCategory
      }
      
      return updatedCategory
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新分类失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 删除分类
  const deleteCategory = async (id: string) => {
    loading.value = true
    error.value = null
    
    try {
      // 检查是否有子分类
      const children = getChildren(id)
      if (children.length > 0) {
        throw new Error('该分类下有子分类，无法删除')
      }
      
      await categoryStorage.delete(id)
      categories.value = categories.value.filter(cat => cat.id !== id)
      
      // 如果删除的是当前选中的分类，清除选择
      if (selectedCategoryId.value === id) {
        selectedCategoryId.value = null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除分类失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 移动分类
  const moveCategory = async (id: string, newParentId: string | null) => {
    return updateCategory(id, { parentId: newParentId })
  }

  // 选择分类
  const selectCategory = (id: string | null) => {
    selectedCategoryId.value = id
  }

  // 获取分类
  const getCategory = (id: string) => {
    return categoryMap.value.get(id)
  }

  return {
    // 状态
    categories,
    loading,
    error,
    selectedCategoryId,
    
    // 计算属性
    rootCategories,
    activeCategories,
    categoryMap,
    
    // 方法
    getChildren,
    getCategoryPath,
    loadCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    moveCategory,
    selectCategory,
    getCategory
  }
})
