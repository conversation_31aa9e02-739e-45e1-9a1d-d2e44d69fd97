import { defineStore } from 'pinia'
import { computed } from 'vue'
import { useLogStore } from './log'
import { useCategoryStore } from './category'
import { getMonthRange, getWeekRange } from '../utils/date'
import type { Summary, CategoryStats, MoodStats } from '../types'

export const useSummaryStore = defineStore('summary', () => {
  const logStore = useLogStore()
  const categoryStore = useCategoryStore()

  // 计算属性
  const summary = computed<Summary>(() => {
    const logs = logStore.logs
    const categories = categoryStore.categories

    const totalLogs = logs.length
    const completedLogs = logs.filter(log => log.isCompleted).length
    const pendingLogs = totalLogs - completedLogs

    // 分类统计
    const categoryStats: CategoryStats[] = categories.map(category => {
      const categoryLogs = logs.filter(log => log.categoryId === category.id)
      const completedCount = categoryLogs.filter(log => log.isCompleted).length
      
      return {
        categoryId: category.id,
        categoryName: category.name,
        logCount: categoryLogs.length,
        completedCount,
        color: category.color
      }
    }).filter(stat => stat.logCount > 0)

    // 心情统计
    const moodCounts = logs.reduce((acc, log) => {
      acc[log.mood] = (acc[log.mood] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const moodStats: MoodStats[] = Object.entries(moodCounts).map(([mood, count]) => ({
      mood: mood as any,
      count,
      percentage: Math.round((count / totalLogs) * 100)
    }))

    return {
      totalLogs,
      completedLogs,
      pendingLogs,
      categoryStats,
      moodStats,
      dateRange: {
        start: new Date(Math.min(...logs.map(log => log.date.getTime()))),
        end: new Date(Math.max(...logs.map(log => log.date.getTime())))
      }
    }
  })

  const weeklySummary = computed(() => {
    const weekRange = getWeekRange()
    const weekLogs = logStore.logs.filter(log => {
      const logDate = new Date(log.date)
      return logDate >= weekRange.start && logDate <= weekRange.end
    })

    return {
      total: weekLogs.length,
      completed: weekLogs.filter(log => log.isCompleted).length,
      pending: weekLogs.filter(log => !log.isCompleted).length
    }
  })

  const monthlySummary = computed(() => {
    const monthRange = getMonthRange()
    const monthLogs = logStore.logs.filter(log => {
      const logDate = new Date(log.date)
      return logDate >= monthRange.start && logDate <= monthRange.end
    })

    return {
      total: monthLogs.length,
      completed: monthLogs.filter(log => log.isCompleted).length,
      pending: monthLogs.filter(log => !log.isCompleted).length
    }
  })

  return {
    summary,
    weeklySummary,
    monthlySummary
  }
})
