<template>
  <div class="min-h-screen bg-base-200">
    <!-- 顶部导航栏 -->
    <header class="navbar bg-base-100 shadow-sm">
      <div class="navbar-start">
        <div class="dropdown">
          <label tabindex="0" class="btn btn-ghost lg:hidden">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16" />
            </svg>
          </label>
          <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
            <li><router-link to="/">首页</router-link></li>
            <li><router-link to="/about">关于</router-link></li>
          </ul>
        </div>
        <router-link to="/" class="btn btn-ghost normal-case text-xl">Daily Log Lite</router-link>
      </div>
      
      <div class="navbar-center hidden lg:flex">
        <ul class="menu menu-horizontal px-1">
          <li><router-link to="/">首页</router-link></li>
          <li><router-link to="/about">关于</router-link></li>
        </ul>
      </div>
      
      <div class="navbar-end">
        <button class="btn btn-ghost btn-circle" @click="handleExport">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
        </button>
        <button class="btn btn-ghost btn-circle" @click="toggleTheme">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
          </svg>
        </button>
      </div>
    </header>

    <div class="flex">
      <!-- 侧边栏 -->
      <aside class="w-64 bg-base-100 min-h-screen hidden lg:block">
        <div class="p-4">
          <h2 class="text-lg font-semibold mb-4">分类管理</h2>
          <CategoryTree />
        </div>
      </aside>

      <!-- 主内容区域 -->
      <main class="flex-1 p-4">
        <div class="max-w-6xl mx-auto">
          <router-view />
        </div>
      </main>
    </div>

    <!-- 移动端侧边栏 -->
    <div class="drawer drawer-mobile">
      <input id="mobile-sidebar" type="checkbox" class="drawer-toggle" v-model="mobileSidebarOpen" />
      <div class="drawer-content">
        <!-- 内容已在上面 -->
      </div>
      <div class="drawer-side">
        <label for="mobile-sidebar" class="drawer-overlay"></label>
        <div class="menu p-4 w-80 h-full bg-base-100">
          <h2 class="text-lg font-semibold mb-4">分类管理</h2>
          <CategoryTree />
        </div>
      </div>
    </div>

    <!-- 导出模态框 -->
    <div v-if="showExportModal" class="modal modal-open">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">导出数据</h3>
        
        <div class="form-control">
          <label class="label">
            <span class="label-text">导出格式</span>
          </label>
          <select v-model="exportFormat" class="select select-bordered">
            <option value="json">JSON</option>
            <option value="csv">CSV</option>
            <option value="txt">Markdown</option>
          </select>
        </div>
        
        <div class="modal-action">
          <button class="btn" @click="showExportModal = false">取消</button>
          <button class="btn btn-primary" @click="confirmExport">导出</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useLogStore } from '../stores/log'
import { useCategoryStore } from '../stores/category'
import { exportData } from '../utils/export'
import CategoryTree from '../components/CategoryTree.vue'

const logStore = useLogStore()
const categoryStore = useCategoryStore()

const mobileSidebarOpen = ref(false)
const showExportModal = ref(false)
const exportFormat = ref<'json' | 'csv' | 'txt'>('json')

const handleExport = () => {
  showExportModal.value = true
}

const confirmExport = async () => {
  await exportData(
    {
      format: exportFormat.value,
      dateRange: undefined,
      categories: undefined,
      includeCompleted: undefined
    },
    logStore.logs,
    categoryStore.categories
  )
  showExportModal.value = false
}

const toggleTheme = () => {
  const html = document.documentElement
  if (html.getAttribute('data-theme') === 'dark') {
    html.setAttribute('data-theme', 'light')
  } else {
    html.setAttribute('data-theme', 'dark')
  }
}
</script>

<style scoped>
.navbar {
  position: sticky;
  top: 0;
  z-index: 100;
}
</style>
