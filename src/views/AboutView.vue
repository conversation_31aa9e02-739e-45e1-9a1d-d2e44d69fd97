<template>
  <div class="max-w-4xl mx-auto">
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title text-3xl mb-4">关于 Daily Log Lite</h2>
        
        <div class="space-y-4">
          <div>
            <h3 class="text-xl font-semibold mb-2">项目介绍</h3>
            <p class="text-gray-700">
              Daily Log Lite 是一个超轻量级的日志登记系统，纯前端实现，零后端依赖。
              专为个人和小团队设计，用于每日工作归纳和周报素材收集。
            </p>
          </div>

          <div>
            <h3 class="text-xl font-semibold mb-2">核心功能</h3>
            <ul class="list-disc list-inside space-y-1 text-gray-700">
              <li>分类管理：支持多级分类，拖拽排序</li>
              <li>日志记录：支持标题、内容、心情、优先级等字段</li>
              <li>数据汇总：按日、周、月统计，可视化展示</li>
              <li>数据导出：支持 JSON、CSV、Markdown 格式</li>
              <li>离线使用：PWA 支持，断网也能正常使用</li>
            </ul>
          </div>

          <div>
            <h3 class="text-xl font-semibold mb-2">技术栈</h3>
            <div class="grid grid-cols-2 gap-2 text-sm">
              <div><strong>框架：</strong>Vue 3.4 + Vite 5</div>
              <div><strong>状态管理：</strong>Pinia</div>
              <div><strong>UI 框架：</strong>DaisyUI + TailwindCSS</div>
              <div><strong>存储：</strong>IndexedDB (localForage)</div>
              <div><strong>图标：</strong>Heroicons</div>
              <div><strong>工具库：</strong>date-fns, uuid, file-saver</div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-semibold mb-2">使用说明</h3>
            <ol class="list-decimal list-inside space-y-1 text-gray-700">
              <li>创建分类：点击左侧分类管理中的"添加分类"按钮</li>
              <li>记录日志：点击"新建日志"按钮，填写相关信息</li>
              <li>查看汇总：切换到"数据汇总"标签页查看统计信息</li>
              <li>导出数据：使用顶部菜单中的导出功能</li>
            </ol>
          </div>

          <div>
            <h3 class="text-xl font-semibold mb-2">快捷键</h3>
            <ul class="list-disc list-inside space-y-1 text-gray-700">
              <li><kbd class="kbd kbd-sm">Ctrl+S</kbd> 保存日志</li>
              <li><kbd class="kbd kbd-sm">Ctrl+N</kbd> 新建日志</li>
              <li><kbd class="kbd kbd-sm">Ctrl+Enter</kbd> 快速提交</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
