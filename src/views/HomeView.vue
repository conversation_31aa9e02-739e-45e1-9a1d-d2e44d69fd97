<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center">
      <h1 class="text-2xl font-bold text-gray-900">我的日志</h1>
      <button 
        @click="showEditor = true"
        class="btn btn-primary"
      >
        新建日志
      </button>
    </div>

    <!-- 分类筛选 -->
    <div class="flex items-center space-x-4">
      <label class="text-sm font-medium text-gray-700">分类筛选:</label>
      <select 
        v-model="selectedCategory"
        class="select select-bordered select-sm"
      >
        <option value="">全部分类</option>
        <option 
          v-for="category in categoryStore.categories" 
          :key="category.id" 
          :value="category.id"
        >
          {{ category.name }}
        </option>
      </select>
    </div>

    <!-- 标签页切换 -->
    <div class="tabs tabs-boxed">
      <a class="tab" :class="{ 'tab-active': activeTab === 'logs' }" @click="activeTab = 'logs'">日志列表</a>
      <a class="tab" :class="{ 'tab-active': activeTab === 'summary' }" @click="activeTab = 'summary'">数据汇总</a>
    </div>

    <!-- 日志列表 -->
    <div v-if="activeTab === 'logs'">
      <LogList 
        :logs="filteredLogs"
        :loading="loading"
        @edit="handleEdit"
        @delete="handleDelete"
      />
    </div>

    <!-- 数据汇总 -->
    <div v-else>
      <SummaryPanel />
    </div>

    <!-- 日志编辑器模态框 -->
    <div v-if="showEditor" class="modal modal-open">
      <div class="modal-box max-w-2xl">
        <LogEditor 
          :log="editingLog || undefined"
          @save="handleSave"
          @cancel="handleCancel"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useLogStore } from '../stores/log'
import { useCategoryStore } from '../stores/category'
import LogList from '../components/LogList.vue'
import LogEditor from '../components/LogEditor.vue'
import SummaryPanel from '../components/SummaryPanel.vue'
import type { Log } from '../types'

const logStore = useLogStore()
const categoryStore = useCategoryStore()

const loading = ref(false)
const showEditor = ref(false)
const editingLog = ref<Log | null>(null)
const selectedCategory = ref('')
const activeTab = ref<'logs' | 'summary'>('logs')

// 计算过滤后的日志列表
const filteredLogs = computed(() => {
  let logs = [...logStore.logs]
  
  // 按分类筛选
  if (selectedCategory.value) {
    logs = logs.filter(log => log.categoryId === selectedCategory.value)
  }
  
  // 按时间倒序排序
  return logs.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
})

// 处理编辑
const handleEdit = (log: Log) => {
  editingLog.value = log
  showEditor.value = true
}

// 处理删除
const handleDelete = async (log: Log) => {
  if (confirm('确定要删除这条日志吗？')) {
    await logStore.deleteLog(log.id)
  }
}

// 处理保存
const handleSave = async () => {
  showEditor.value = false
  editingLog.value = null
}

// 处理取消
const handleCancel = () => {
  showEditor.value = false
  editingLog.value = null
}

// 加载数据
onMounted(async () => {
  loading.value = true
  await Promise.all([
    logStore.loadLogs(),
    categoryStore.loadCategories()
  ])
  loading.value = false
})
</script>
