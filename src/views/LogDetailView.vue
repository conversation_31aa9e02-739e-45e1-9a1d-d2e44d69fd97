<template>
  <div v-if="log" class="max-w-4xl mx-auto">
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <div class="flex justify-between items-start">
          <div>
            <h2 class="card-title text-2xl">{{ log.title }}</h2>
            <p class="text-sm text-gray-500">{{ formatDate(log.date) }}</p>
          </div>
          <div class="badge" :class="getMoodBadgeClass(log.mood)">
            {{ getMoodLabel(log.mood) }}
          </div>
        </div>

        <div class="flex items-center gap-2 mb-4">
          <div class="badge badge-outline">{{ getCategoryName(log.categoryId) }}</div>
          <div v-for="tag in log.tags" :key="tag" class="badge badge-ghost">
            {{ tag }}
          </div>
          <div class="badge" :class="log.isCompleted ? 'badge-success' : 'badge-warning'">
            {{ log.isCompleted ? '已完成' : '待完成' }}
          </div>
        </div>

        <div class="prose max-w-none">
          <p>{{ log.content }}</p>
        </div>

        <div class="divider"></div>

        <div class="text-sm text-gray-500">
          <p>创建时间：{{ formatDate(log.createdAt, 'yyyy-MM-dd HH:mm') }}</p>
          <p v-if="log.updatedAt !== log.createdAt">
            更新时间：{{ formatDate(log.updatedAt, 'yyyy-MM-dd HH:mm') }}
          </p>
        </div>

        <div class="card-actions justify-end">
          <button class="btn btn-ghost" @click="$router.push('/')">返回</button>
          <button class="btn btn-primary" @click="handleEdit">编辑</button>
          <button class="btn btn-error" @click="handleDelete">删除</button>
        </div>
      </div>
    </div>
  </div>

  <div v-else class="text-center py-8">
    <div class="text-gray-500">日志不存在</div>
    <button class="btn btn-ghost mt-4" @click="$router.push('/')">返回首页</button>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useLogStore } from '../stores/log'
import { useCategoryStore } from '../stores/category'
import { formatDate } from '../utils/date'

const route = useRoute()
const router = useRouter()
const logStore = useLogStore()
const categoryStore = useCategoryStore()

const log = computed(() => {
  const id = route.params.id as string
  return logStore.getLog(id)
})

const getCategoryName = (categoryId: string) => {
  const category = categoryStore.getCategory(categoryId)
  return category?.name || '未分类'
}

const getMoodBadgeClass = (mood: string) => {
  const classes = {
    happy: 'badge-success',
    sad: 'badge-error',
    angry: 'badge-warning',
    neutral: 'badge-ghost',
    excited: 'badge-info',
    tired: 'badge-secondary',
    anxious: 'badge-warning'
  }
  return classes[mood as keyof typeof classes] || 'badge-ghost'
}

const getMoodLabel = (mood: string) => {
  const labels = {
    happy: '开心',
    sad: '难过',
    angry: '生气',
    neutral: '平静',
    excited: '兴奋',
    tired: '疲惫',
    anxious: '焦虑'
  }
  return labels[mood as keyof typeof labels] || mood
}

const handleEdit = () => {
  if (log.value) {
    router.push(`/log/${log.value.id}/edit`)
  }
}

const handleDelete = async () => {
  if (log.value && confirm('确定要删除这条日志吗？')) {
    await logStore.deleteLog(log.value.id)
    router.push('/')
  }
}

onMounted(async () => {
  await Promise.all([
    logStore.loadLogs(),
    categoryStore.loadCategories()
  ])
})
</script>
