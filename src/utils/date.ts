import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

export const formatDate = (date: Date | string, formatStr = 'yyyy-MM-dd'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return format(dateObj, formatStr, { locale: zhCN })
}

export const formatRelativeDate = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffInDays = Math.floor((now.getTime() - dateObj.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffInDays === 0) return '今天'
  if (diffInDays === 1) return '昨天'
  if (diffInDays === 2) return '前天'
  if (diffInDays <= 7) return `${diffInDays}天前`
  
  return formatDate(dateObj, 'MM-dd')
}

export const getWeekRange = (date: Date = new Date()) => {
  const startOfWeek = new Date(date)
  startOfWeek.setDate(date.getDate() - date.getDay())
  startOfWeek.setHours(0, 0, 0, 0)
  
  const endOfWeek = new Date(startOfWeek)
  endOfWeek.setDate(startOfWeek.getDate() + 6)
  endOfWeek.setHours(23, 59, 59, 999)
  
  return { start: startOfWeek, end: endOfWeek }
}

export const getMonthRange = (date: Date = new Date()) => {
  const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1)
  startOfMonth.setHours(0, 0, 0, 0)
  
  const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0)
  endOfMonth.setHours(23, 59, 59, 999)
  
  return { start: startOfMonth, end: endOfMonth }
}
