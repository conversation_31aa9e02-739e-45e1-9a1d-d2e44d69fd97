import { saveAs } from 'file-saver'
import type { Log, Category, ExportConfig } from '../types'
import { formatDate } from './date'

export const exportToJson = (data: any, filename: string) => {
  const json = JSON.stringify(data, null, 2)
  const blob = new Blob([json], { type: 'application/json;charset=utf-8' })
  saveAs(blob, `${filename}.json`)
}

export const exportToCsv = (logs: Log[], categories: Category[], filename: string) => {
  const headers = ['日期', '标题', '内容', '分类', '心情', '优先级', '标签', '状态', '创建时间']
  const rows = logs.map(log => {
    const category = categories.find(c => c.id === log.categoryId)
    return [
      formatDate(log.date),
      log.title,
      log.content.replace(/"/g, '""'),
      category?.name || '未分类',
      log.mood,
      log.priority,
      log.tags.join(';'),
      log.isCompleted ? '已完成' : '待完成',
      formatDate(log.createdAt, 'yyyy-MM-dd HH:mm')
    ]
  })

  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(','))
    .join('\n')

  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' })
  saveAs(blob, `${filename}.csv`)
}

export const exportToMarkdown = (logs: Log[], categories: Category[], filename: string) => {
  let markdown = '# 日志汇总\n\n'
  
  // 按日期分组
  const logsByDate = logs.reduce((acc, log) => {
    const dateKey = formatDate(log.date)
    if (!acc[dateKey]) acc[dateKey] = []
    acc[dateKey].push(log)
    return acc
  }, {} as Record<string, Log[]>)

  Object.entries(logsByDate)
    .sort(([a], [b]) => b.localeCompare(a))
    .forEach(([date, dayLogs]) => {
      markdown += `## ${date}\n\n`
      
      dayLogs.forEach(log => {
        const category = categories.find(c => c.id === log.categoryId)
        markdown += `### ${log.title}\n\n`
        markdown += `- **分类**: ${category?.name || '未分类'}\n`
        markdown += `- **心情**: ${log.mood}\n`
        markdown += `- **优先级**: ${log.priority}\n`
        markdown += `- **标签**: ${log.tags.join(', ')}\n`
        markdown += `- **状态**: ${log.isCompleted ? '已完成' : '待完成'}\n`
        markdown += `- **创建时间**: ${formatDate(log.createdAt, 'yyyy-MM-dd HH:mm')}\n\n`
        markdown += `${log.content}\n\n`
        markdown += '---\n\n'
      })
    })

  const blob = new Blob([markdown], { type: 'text/markdown;charset=utf-8' })
  saveAs(blob, `${filename}.md`)
}

export const exportData = async (config: ExportConfig, logs: Log[], categories: Category[]) => {
  let filteredLogs = logs
  
  if (config.dateRange) {
    filteredLogs = filteredLogs.filter((log: Log) => 
      log.date >= config.dateRange!.start && log.date <= config.dateRange!.end
    )
  }
  
  if (config.categories && config.categories.length > 0) {
    filteredLogs = filteredLogs.filter((log: Log) => 
      config.categories!.includes(log.categoryId)
    )
  }
  
  if (config.includeCompleted !== undefined) {
    filteredLogs = filteredLogs.filter((log: Log) => 
      log.isCompleted === config.includeCompleted
    )
  }
  
  const filename = `daily-log-${formatDate(new Date())}`
  
  switch (config.format) {
    case 'json':
      exportToJson({ logs: filteredLogs, categories }, filename)
      break
    case 'csv':
      exportToCsv(filteredLogs, categories, filename)
      break
    case 'txt':
      exportToMarkdown(filteredLogs, categories, filename)
      break
  }
}
