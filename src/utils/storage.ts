import localforage from 'localforage'
import type { Category, Log, AppSettings } from '../types'

// 配置localforage
const dbConfig = {
  name: 'DailyLogLite',
  version: 1.0,
  storeName: 'daily_logs'
}

// 初始化数据库
const categoriesStore = localforage.createInstance({
  ...dbConfig,
  storeName: 'categories'
})

const logsStore = localforage.createInstance({
  ...dbConfig,
  storeName: 'logs'
})

const settingsStore = localforage.createInstance({
  ...dbConfig,
  storeName: 'settings'
})

// 分类相关操作
export const categoryStorage = {
  async getAll(): Promise<Category[]> {
    const categories: Category[] = []
    await categoriesStore.iterate((value: Category) => {
      categories.push(value)
    })
    return categories.sort((a, b) => a.sortOrder - b.sortOrder)
  },

  async getById(id: string): Promise<Category | null> {
    return await categoriesStore.getItem(id)
  },

  async save(category: Category): Promise<void> {
    await categoriesStore.setItem(category.id, category)
  },

  async delete(id: string): Promise<void> {
    await categoriesStore.removeItem(id)
  },

  async getChildren(parentId: string | null): Promise<Category[]> {
    const categories = await this.getAll()
    return categories.filter(cat => cat.parentId === parentId)
  }
}

// 日志相关操作
export const logStorage = {
  async getAll(): Promise<Log[]> {
    const logs: Log[] = []
    await logsStore.iterate((value: Log) => {
      logs.push(value)
    })
    return logs.sort((a, b) => b.date.getTime() - a.date.getTime())
  },

  async getById(id: string): Promise<Log | null> {
    return await logsStore.getItem(id)
  },

  async getByCategory(categoryId: string): Promise<Log[]> {
    const logs = await this.getAll()
    return logs.filter(log => log.categoryId === categoryId)
  },

  async getByDateRange(startDate: Date, endDate: Date): Promise<Log[]> {
    const logs = await this.getAll()
    return logs.filter(log => {
      const logDate = new Date(log.date)
      return logDate >= startDate && logDate <= endDate
    })
  },

  async save(log: Log): Promise<void> {
    await logsStore.setItem(log.id, log)
  },

  async delete(id: string): Promise<void> {
    await logsStore.removeItem(id)
  },

  async deleteByCategory(categoryId: string): Promise<void> {
    const logs = await this.getByCategory(categoryId)
    await Promise.all(logs.map(log => this.delete(log.id)))
  }
}

// 设置相关操作
export const settingsStorage = {
  async get(): Promise<AppSettings | null> {
    return await settingsStore.getItem('app_settings')
  },

  async save(settings: AppSettings): Promise<void> {
    await settingsStore.setItem('app_settings', settings)
  },

  async getDefault(): Promise<AppSettings> {
    return {
      theme: 'auto',
      defaultCategory: null,
      autoSave: true,
      exportPath: '',
      dateFormat: 'YYYY-MM-DD',
      timeFormat: 'HH:mm'
    }
  }
}

// 数据库工具
export const storageUtils = {
  async clearAll(): Promise<void> {
    await Promise.all([
      categoriesStore.clear(),
      logsStore.clear(),
      settingsStore.clear()
    ])
  },

  async exportData() {
    const [categories, logs, settings] = await Promise.all([
      categoryStorage.getAll(),
      logStorage.getAll(),
      settingsStorage.get()
    ])

    return {
      categories,
      logs,
      settings,
      exportDate: new Date().toISOString()
    }
  },

  async importData(data: any): Promise<void> {
    if (!data.categories || !data.logs) {
      throw new Error('Invalid data format')
    }

    // 清空现有数据
    await this.clearAll()

    // 导入分类
    for (const category of data.categories) {
      await categoryStorage.save(category)
    }

    // 导入日志
    for (const log of data.logs) {
      await logStorage.save(log)
    }

    // 导入设置
    if (data.settings) {
      await settingsStorage.save(data.settings)
    }
  }
}