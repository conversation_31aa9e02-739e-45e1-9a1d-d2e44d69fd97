{"0_项目定位": {"名称": "daily-log-lite", "目标": "超轻量级日志登记系统，纯前端，零后端，一键保存 JSON，清新 UI，秒级响应。", "使用人群": "个人/小团队每日工作归纳、周报素材收集。"}, "1_功能清单": {"1.1_分类体系": {"层级": "大类 → 子类（两级即可，后期可无限扩展）", "操作": "增 / 删 / 改 / 拖拽排序", "存储": "实时同步到 IndexedDB，退出即自动备份 JSON"}, "1.2_日志记录": {"字段": "日期（yyyy-mm-dd）, 大类ID, 子类ID, 内容, 创建时间戳", "快捷键": "Ctrl+S 保存, Ctrl+N 新建, Ctrl+Enter 立即提交"}, "1.3_智能汇总": {"维度": ["按日", "按周", "按大类", "按子类"], "呈现": "可折叠面板 + 关键词云 + 导出 Markdown/CSV/JSON"}, "1.4_数据管理": {"导入": "拖拽 JSON 到窗口自动合并", "导出": "手动导出 / 每日 00:00 自动备份到 Downloads 目录"}}, "2_技术选型": {"运行时": "浏览器 PWA（离线可用）", "框架": "Vue 3.4 + Vite 5", "状态": "Pinia 持久化插件 + IndexedDB（localForage 封装）", "UI": "Naive UI + TailwindCSS + DaisyUI（清新配色 preset: garden）", "图标": "unplugin-icons + tabler-icons", "工具库": "date-fns, uuid, file-saver, lodash-es"}, "3_目录结构": {"src/": {"components/": {"CategoryTree.vue": "分类树（可拖拽）", "LogEditor.vue": "日志输入区", "SummaryPanel.vue": "汇总面板"}, "stores/": {"category.ts": "分类 CRUD + 持久化", "log.ts": "日志 CRUD + 索引", "summary.ts": "汇总计算（computed）"}, "utils/": {"storage.ts": "localForage 封装", "export.ts": "JSON/Markdown/CSV 导出", "date.ts": "date-fns 二次封装"}, "assets/": {"main.css": "Tailwind + DaisyUI 引入"}, "App.vue": "根布局（左右分栏）", "sw.ts": "Service Worker 离线缓存"}}, "4_数据模型": {"Category": {"id": "uuid", "name": "string", "children": ["SubCategory"]}, "SubCategory": {"id": "uuid", "name": "string"}, "Log": {"id": "uuid", "date": "yyyy-mm-dd", "categoryId": "uuid", "subcategoryId": "uuid", "content": "string", "createdAt": "timestamp"}, "StoreShape": {"categories": "Category[]", "logs": "Log[]", "selectedDate": "yyyy-mm-dd"}}, "5_关键流程": {"初始化": ["IndexedDB 打开 → 读取 categories & logs", "若为空则注入示例数据"], "新建分类": ["CategoryTree.vue 右键菜单 → 输入名称 → commit", "Pinia action → 更新 IndexedDB → Tree 自动刷新"], "记录日志": ["DatePicker 选日期 → CategoryCascader 选两级分类 → 输入内容", "LogEditor.vue 内按 Ctrl+Enter → logStore.add(log) → toast 提示保存成功"], "汇总计算": ["summaryStore 监听 logs 变化 → computed 生成 Map<date, Map<category, string[]>>", "SummaryPanel.vue 使用 NaiveUI 折叠面板渲染"], "导出 JSON": ["点击顶部导出按钮 → storage.exportAll() → download(fileName, JSON.stringify(data, null, 2))"]}, "6_界面草图": {"desktop": {"header": ["Logo", "DatePicker", "ThemeToggle", "ExportBtn"], "body": ["左侧 25%: CategoryTree（可折叠）", "右侧 75%: Tabs[记录|汇总]", "记录 Tab: Cascader + Textarea + QuickSave", "汇总 Tab: 折叠面板 + 云图"]}, "mobile": {"body": "Drawer 弹出 CategoryTree，主界面保持 Tabs"}}, "7_开发脚本": {"创建": "npm create vue@latest DailyLog_Lite --typescript --router --pinia --eslint --prettier", "依赖": "cd DailyLog_Lite && npm i naive-ui tailwindcss postcss autoprefixer daisyui localforage uuid file-saver date-fns @vueuse/core", "初始化": "npx tailwindcss init -p && npm i -D @types/uuid @types/file-saver", "运行": "npm run dev", "构建": "npm run build && npx vite-plugin-pwa", "发布": "npm run build && npx serve dist"}, "附录A_示例代码片段": {"Pinia 持久化": "defineStore('log', () => { ... }, { persist: { storage: localForage } })", "导出函数": "export const exportJson = (data: any, file: string) => saveAs(new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'}), file)"}, "附录B_主题色值": {"light": {"primary": "#22c55e", "base-100": "#ffffff", "base-200": "#f7f8f8", "accent": "#0ea5e9"}, "dark": {"primary": "#4ade80", "base-100": "#111827", "base-200": "#1f2937", "accent": "#38bdf8"}}}